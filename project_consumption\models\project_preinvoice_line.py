# project_prefill/models/project_preinvoice_line.py

from odoo import models, fields, api

class ProjectPreinvoiceLine(models.Model):
    _name = 'project.preinvoice.line'
    _description = 'Project Pre-invoice Line'

    project_id = fields.Many2one(
        'project.project',
        string='Project',
        required=True,
        ondelete='cascade'
    )
    product_id = fields.Many2one(
        'product.product',
        string='Product',
        required=True
    )
    quantity = fields.Float(
        string='Quantity',
        default=1.0
    )
    # We use the product's standard price as the "purchase price"
    purchase_price = fields.Float(
        string='Purchase Price',
        related='product_id.standard_price',
        readonly=True,
        store=True # Store it for performance
    )
    total = fields.Monetary(
        string='Total',
        compute='_compute_total',
        store=True
    )
    currency_id = fields.Many2one(
        'res.currency',
        related='project_id.currency_id',
        string='Currency',
        readonly=True
    )

    @api.depends('quantity', 'purchase_price')
    def _compute_total(self):
        for line in self:
            line.total = line.quantity * line.purchase_price