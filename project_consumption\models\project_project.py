from odoo import models, fields, api
from odoo.exceptions import UserError
from odoo.tools.translate import _


class ProjectConsumption(models.Model):
    _inherit = 'project.project'

    dossier_tech = fields.Binary(string='Dossier Technique')
    date_dep_tech = fields.Date(string='Date de dépôt du dossier technique')
    preinvoice_line_ids = fields.One2many(
        'project.preinvoice.line',
        'project_id',
        string='Tableau Préfacture'
    )
    preinvoice_total = fields.Monetary(
        string='Total Préfacture',
        compute='_compute_preinvoice_total',
        currency_field='currency_id'
    )

    @api.depends('preinvoice_line_ids.total')
    def _compute_preinvoice_total(self):
        for project in self:
            project.preinvoice_total = sum(line.total for line in project.preinvoice_line_ids)

    def action_prefill_preinvoice(self):
        """
        Button action to prefill the pre-invoice lines from the
        delivered quantities of the associated sale order.
        """
        self.ensure_one()

        # Find the sale order associated with this project's analytic account
        sale_order = self.env['sale.order'].search([('analytic_account_id', '=', self.analytic_account_id.id)], limit=1)

        if not sale_order:
            raise UserError(_("No Sale Order found associated with this project's analytic account."))

        # Clear existing lines to avoid duplicates on re-click
        self.preinvoice_line_ids.unlink()

        # Create new lines from delivered sale order lines
        lines_to_create = []
        for so_line in sale_order.order_line:
            if so_line.qty_delivered > 0:
                lines_to_create.append({
                    'product_id': so_line.product_id.id,
                    'quantity': so_line.qty_delivered,
                    # purchase_price and total will be computed automatically
                })

        if lines_to_create:
            self.env['project.preinvoice.line'].create(lines_to_create)

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }