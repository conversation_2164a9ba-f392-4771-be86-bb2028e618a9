from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging
_logger = logging.getLogger(__name__)
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def write(self, vals):
        _logger.info("✅ Method called: write")
        _logger.info("vals: %s", vals)
        if 'opportunity_id' in vals:
            opportunity = self.env['crm.lead'].search([('id', '=', vals['opportunity_id'])])
            if opportunity.stage_id.id == 1:
                opportunity.write({'stage_id': 2,'context_crm': True})
        return super().write(vals)

    @api.model
    def create(self, vals):
        _logger.info("✅ Method called: create")
        _logger.info("vals: %s", vals)
        if 'opportunity_id' in vals:
            opportunity = self.env['crm.lead'].search([('id', '=', vals['opportunity_id'])])
            if opportunity.stage_id.id == 1:
                opportunity.write({'stage_id': 2,'context_crm': True})
        return super().create(vals)

    def action_confirm(self):
        res = super(<PERSON>Order, self).action_confirm()
        for order in self:
            if order.opportunity_id:
                if order.opportunity_id.contract_signed == False:
                    raise ValidationError(_("Contract must be signed"))
        return res

