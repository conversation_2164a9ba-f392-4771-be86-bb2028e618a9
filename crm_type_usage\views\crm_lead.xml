<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="type_usage" model="ir.ui.view">
        <field name="name">ccrm.lead.type_usage</field>
        <field name="model">crm.lead</field>
        <field name="inherit_id" ref="crm.crm_lead_view_form" />
        <field name="arch" type="xml">

            <xpath expr="//button[@name='action_set_won_rainbowman']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[@class='oe_title']/h1" position="after">
                <group>
                    <field name="installation_type" string="Type d\'installation" widget="selection"/>
                </group>
                <group attrs="{'invisible': [('stage_id', '=', 1)]}" >
                    <field name="order_ids" invisible="1"/>
                    <field name="selected_order_id"
                   domain="[('id', 'in', order_ids)]"
                   context="{'default_opportunity_id': active_id}" />
                    <button name="valider_propo" string="Valider Proposition" type="object" class="oe_highlight"  groups="crm_type_usage.group_sale_manager"/>
                </group>
            </xpath>
<!--             <xpath expr="//label[@for='probability']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
            <xpath expr="//label[@for='probability']" position="after">
                <label for="power_level"/>
            </xpath> -->
            <xpath expr="//group[@name='lead_priority']" position="after">
                <group string="Informations" attrs="{'invisible': ['|', ('installation_type', '!=', 'connected'), ('stage_id', '=', 1)]}">
                    <field name="project_state" invisible="1"/>
                    <field name="typ_paie"/>
                    <field name="comptant"/>
                    <field name="rec_equi" attrs="{'invisible': [('typ_paie', '!=', 'comptant')]}"/>
                    <field name="fin_trav" attrs="{'invisible': [('typ_paie', '!=', 'comptant')]}"/>
                    <field name="apr_rec" attrs="{'invisible': [('typ_paie', '!=', 'comptant')]}"/>
                    <field name="cred_steg" attrs="{'invisible': ['|', ('typ_paie', '!=', 'credit'), ('installation_type', '!=', 'connected')]}"/>
                    <field name="nb_mensualite" attrs="{'invisible': [('typ_paie', '=', 'comptant')]}"/>
                    <field name="montant_mensualite" attrs="{'invisible': [('typ_paie', '=', 'comptant')]}"/>
                    <field name="apres_recep" attrs="{'invisible': [('typ_paie', '!=', 'comptant')]}"/>
                    <field name="date_prem_eche" attrs="{'invisible': [('typ_paie', '=', 'comptant')]}"/>
                </group>
                <group string="Informations" attrs="{'invisible': [('installation_type', '!=', 'pompage')]}">
                    <field name="typ_paie_pompage"/>
                    <field name="comptant"/>
                    <field name="nb_mensualite" attrs="{'invisible': [('typ_paie', '=', 'comptant')]}"/>
                    <field name="montant_mensualite" attrs="{'invisible': [('typ_paie', '=', 'comptant')]}"/>
                    <field name="apres_recep" attrs="{'invisible': [('typ_paie', '!=', 'comptant')]}"/>
                    <field name="config_projet"/>
                    <field name="date_prem_eche"/>
                </group>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="Contrat" name="Contrat">
                    <group>
                        <div class="o_horizontal" style="display: flex; align-items: center; gap: 10px;" attrs="{'invisible': [('installation_type', '!=', 'connected')]}">&gt;
                            <label for="contrat_generated" string="Contrat généré"/>
                            <field name="contrat_generated" readonly="1"/>
                            <button name="generate_contrat" string="Générer Contrat" type="object" class="oe_highlight" attrs="{'invisible': [('contrat_generated', '=', True)]}"/>
                        </div>
                        <field name="contract"/>
                        <field name="contract_signed" readonly="1"/>
                        <field name="contract_validated" groups="crm_type_usage.group_sale_manager"/>
                        <label for="attachment_ids" string="Paiements"/>
                        <field nolabel="1" name="attachment_ids" widget="many2many_binary"/>
                    </group>
                </page>
                <page string="Dossier Commercial" name="Dossier Commercial" attrs="{'invisible': ['|', ('installation_type', '!=', 'connected'), ('stage_id', 'not in', [4, 6, 7, 10])]}">
                    <group attrs="{'invisible': [('installation_type', '!=', 'connected')]}">
                        <group>
                            <field name="date_dep_commercial"/>
                            <field name="doc_decharge"/>
                        </group>
                        <group>
                            <field name="date_appro_commercial"/>
                            <field name="doc_decharg_paiement"/>
                            <field name="taux_interet"/>
                        </group>
                    </group>
                    <group attrs="{'invisible': [('installation_type', '!=', 'pompage')]}">
                    </group>
                </page>
                <page string="dossier Technique" name="Dossier Technique" attrs="{'invisible': ['|', ('installation_type', '!=', 'connected'), ('stage_id', 'not in', [4, 6, 7, 10])]}">
                    <group attrs="{'invisible': [('installation_type', '!=', 'connected')]}">
                        <field name="dossier_tech"/>
                        <field name="date_dep_tech"/>
                    </group>
                </page>
                <page string="Consommation" name="Consommation" attrs="{'invisible': [('project_state', '=', 'non_demarre')]}">
                    <field name="project_comsumption_line" mode="tree">
                        <tree string="Comsumption" editable="False">
                            <control>
                                <create name="add_product_control" string="Add a product"/>
                            </control>
                            <field name="product_uom_category_id" invisible="1"/>
                            <field name="product_id" force_save="1" context="{                                                 'partner_id': parent.partner_id,                                                 'quantity': product_uom_qty,                                                 'uom':product_uom,                                                 'company_id': parent.company_id,                                             }" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" widget="product_configurator"/>
                            <field name="product_template_id" string="Product" invisible="1" context="{                                               'partner_id': parent.partner_id,                                               'quantity': product_uom_qty,                                               'uom':product_uom,                                               'company_id': parent.company_id,                                           }" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" widget="product_configurator"/>
                            <field name="product_uom_qty" context="{                                                 'partner_id': parent.partner_id,                                                 'quantity': product_uom_qty,                                                 'uom': product_uom,                                                 'company_id': parent.company_id                                             }"/>
                            <field name="product_uom" force_save="1" string="UoM" context="{'company_id': parent.company_id}" groups="uom.group_uom" options="{&quot;no_open&quot;: True}" optional="show"/>
                            <field name="company_id" invisible="1"/>
                        </tree>
                    </field>
                </page>
               <page string="Visite Technique Raccordée" name="Visite Technique Raccordée" attrs="{'invisible': [('installation_type', '!=', 'connected')]}">
                    <group>
                        <group string="Section 1 : Identification Client">
                            <field name="coord_gps"/>
                            <field name="date_visite_tech"/>
                            <field name="district"/>
                        </group>
                        <group string="Section 2 : Informations">
                            <field name="sys_mont"/>
                            <field name="inc_charp"/>
                            <field name="risq_omb"/>
                        </group>
                        <group string="Section 3 : Logistique">
                            <field name="typ_compt"/>
                            <field name="cal_dis"/>
                            <field name="photo_dis"/>
                            <field name="photo_compteur"/>
                        </group>
                        <group string="Section 4 : Partie Electrique">
                            <field name="long_toit"/>
                            <field name="larg_toit"/>
                            <field name="phot_toit"/>
                            <field name="dis_ac"/>
                            <field name="dis_dc"/>
                            <field name="dis_st_rg"/>
                            <field name="empl_tech"/>
                            <field name="phot_emp_tech"/>
                        </group>
                        <group string="Section 5 : Partie Mécanique">
                            <field name="typ_mat"/>
                            <field name="typ_str"/>
                            <field name="haut_st"/>
                            <field name="incli_struc"/>
                            <field name="nb_niv_stru"/>
                            <field name="comments"/>
                        </group>
                        <group string="Visite Technique">
                            <field name="visite_joint"/>
                            <button name="valider_visite_tech" string="Valider Visite" type="object" class="oe_highlight" attrs="{'invisible': [('visite_joint', '=', False)]}"/>
                        </group>
                    </group>
                </page>
                <page string="Visite Technique Pompage" name="visite_technique_pompage_id" id="visite_technique_pompage_id" attrs="{'invisible': [('installation_type', '!=', 'pompage')]}">
           
                    <group>
                        <group string="Section 1 : Identification Client">
                            <field name="partner_id"/>
                            <field name="coord_gps" string="Coordonnées GPS"/>
                            <field name="tel"/>
                            <field name="steg_exis"/>
                            <field name="typ_compt"/>
                            <field name="surface_terrain"/>
                        </group>
                        <group string="Informations">
                            <field name="sys_mont" string="Système de montage" widget="selection"/>
                            <field name="prep_terrain" attrs="{'invisible': [('sys_mont', '!=', 'sol')]}"/>
                            <field name="fix_sol" attrs="{'invisible': [('sys_mont', '!=', 'sol')]}"/>
                            <field name="risq_omb" string="Risque d'ombrage" widget="selection"/>
                            <field name="typ_projet"/>
                            <field name="fich_tech_sonde" attrs="{'invisible': [('typ_projet', 'not in', ['anme','apia'])]}"/>

                        </group>
                    </group>
                    <group string="Section 2 : Partie Electrique">
                        <group string="Pompe">
                            <field name="pomp_exist" string="Existence de la pompe"/>
                            <field name="puissance_pompe" string="Puissance de la pompe" attrs="{'invisible': [('pomp_exist', '!=', 'oui')]}"/>
                            <field name="fich_tech_pompe" string="Fiche technique de la pompe" attrs="{'invisible': [('pomp_exist', '!=', 'oui')]}"/>
                        </group>
                        <group string="Sondage">
                            <field name="prof_immer" string="Profondeur d'immersion"/>
                            <field name="typ_ref" string="Type de refoulement" widget="selection"/>
                            <field name="dis_ref" string="Distance de refoulement" attrs="{'invisible': [('typ_ref', '!=', 'direct')]}"/>
                            <field name="haut_res" string="Haut de réservoir" attrs="{'invisible': [('typ_ref', '=', 'direct')]}"/>
                            <field name="dist_puit_bassin" string="Distance entre le puits et le bassin" attrs="{'invisible': [('typ_ref', '=', 'direct')]}"/>
                        </group>
                    </group>
                    <group>
                        <group string="Local Technique">
                            <field name="dis_panneau_loc" string="Distance entre les panneaux et le local technique"/>
                            <field name="dis_loc_puit" string="Distance entre le local technique et le puits"/>
                            <field name="emp_loc_tech" string="Emplacement du local technique"/>
                            <field name="sys_comp" string="Existance du système de compensation" widget="selection"/>
                        </group>
                        <group string="Section 4 : Partie Mécanique">
                            <field name="nb_stru" string="Nombre de structure"/>
                            <field name="typ_mat_stru" string="Type Matériaux pour Structure" widget="selection"/>
                            <field name="typ_stru" string="Type Structure" widget="selection"/>
                            <field name="haut_trap" string="Haut de trapèze" widget="selection" attrs="{'invisible': [('typ_stru', '!=', 'trapeze')]}"/>
                            <field name="inc_stru" string="Inclinaison de la structure" widget="selection"/>
                            <field name="nb_niv_stru" string="Nombre de niveaux de structure" widget="selection"/>
                        </group>
                        <group string="Visite Technique">
                            <field name="visite_joint"/>
                        </group>
                    </group>
                </page>
                <page string="Rapport Chantier" name="Rapport Chantier" attrs="{'invisible': ['|', ('installation_type', '!=', 'connected'), ('stage_id', 'not in', [4, 6, 7, 10])]}">
                    <field name="receptionne"/>
                    <field name="pv_reception"/>
                    <field name="date_mise_serv"/>
                </page>
                <page string="Rapport Chantier" name="Rapport Chantier" attrs="{'invisible': ['|', ('installation_type', '!=', 'pompage'), ('stage_id', 'not in', [4, 6, 7, 10])]}">
                    <field name="date_mise_serv"/>
                </page>
            </xpath>
        </field>
    </record>
</odoo>