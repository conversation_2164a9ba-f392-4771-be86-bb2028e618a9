from odoo import models, fields, api


class CrmLead(models.Model):
    _inherit = 'crm.lead'

    sub_anme = fields.Monetary(string='Subvention ANME')
    cred_steg = fields.Monetary(string='Crédit STEG')

    prod_annuel = fields.Float(string='Production annuelle',compute='_compute_prod_annuel')
    coefficient = fields.Float(string='Coefficient')

    @api.depends('coefficient','power_level')
    def _compute_prod_annuel(self):
        for rec in self:
            if not rec.coefficient:
                rec.coefficient = 1700
            rec.prod_annuel = rec.coefficient * rec.power_level