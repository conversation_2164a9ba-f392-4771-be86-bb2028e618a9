from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging
import base64
import re

_logger = logging.getLogger(__name__)
class CrmLead(models.Model):
    _inherit = 'crm.lead'

    installation_type = fields.Selection([('connected','RACCORDE'),('pompage','POMPAGE')], string="Type d'installation")
    project_created = fields.Boolean(string='Projet créé',default=False)
    
    def create_project_task(self):
        if self.project_created:
            raise UserError(_('Un projet a déjà été créé pour ce lead'))
        if self.installation_type:
            project_type = 'RACCORDE' if self.installation_type == 'connected' else 'POMPAGE'
        else:
            raise UserError(_('Veuillez sélectionner le type d\'installation'))
        project_id = self.env['project.project'].search([('name', '=', project_type)])
        project_task = self.env['project.task'].create({
            'name': 'Projet de ' + self.name,
            'partner_id': self.partner_id.id,
            'project_id': project_id.id,
        })
        

    def action_sale_quotations_new(self):
        _logger.info("✅ Method called: action_sale_quotations_new")
        _logger.info("Current sale_order_count: %s", self.sale_order_count)

        # Ensure first quotation updates stage to proposition
        if self.sale_order_count >= 1:
            raise UserError(_("Une commande de vente confirmée existe déjà."))

        # Call the parent method to open the sale order form
        return super().action_sale_quotations_new()

    project_state= fields.Selection([
        ('non_demarre', 'Non Démarré'),
        ('nouveau', 'Nouveau'),
        ('ouvert', 'Ouvert'),
        ('cloture', 'Clôturé'),
    ], string='Etat Projet', default='non_demarre')

    doc_opip = fields.Binary(string='Document OPIP')
    doc_decharge = fields.Binary(string='Document Décharge')
    taux_interet = fields.Float(string='Taux d\'intérêt')
    date_appro_commercial = fields.Date(string='Date Approbation Commercial')
    doc_decharg_paiement = fields.Binary(string='Document Déchargement Paiement')
    date_rec = fields.Date(string='Date Réception')
    dossier_tech = fields.Binary(string='Dossier Technique')
    date_dep_commercial = fields.Date(string='Date Dépôt Commercial')
    sub_anme = fields.Monetary(string='Subvention ANME')
    cred_steg = fields.Monetary(string='Crédit STEG')
    comptant = fields.Monetary(string='A la Signature du Contrat')
    rec_equi = fields.Monetary(string="A la réception des équipemtns")
    fin_trav = fields.Monetary(string="A la Fin des Travaux")
    apr_rec = fields.Monetary(string="Après récepion STEG")
    typ_paie = fields.Selection([
        ('comptant', 'Comptant'),
        ('credit', 'Crédit'),
        ('mensuel', 'Mensuel'),
    ], string='Type Paiement')
    typ_paie_pompage = fields.Selection([
        ('comptant', 'Comptant'),
        ('mensuel', 'Mensuel'),
    ], string='Type Paiement')
    @api.onchange('installation_type')
    def _onchange_installation_type(self):
        if self.installation_type == 'pompage':
            return {
                'domain': {
                    'typ_paie': [('value', 'in', ['comptant', 'mensuel'])]
                }
            }
        else:
            return {
                'domain': {
                    'typ_paie': [('value', 'in', ['comptant', 'credit', 'mensuel'])]
                }
            }
            
    engagement = fields.Monetary(string="Engagement Client", compute ='_compute_engagement' )
    def _compute_engagement(self):
        self.engagement = self.expected_revenue-self.cred_steg
        
    nb_mensualite = fields.Integer(string='Nombre de Traites')
    montant_mensualite = fields.Monetary(string='Montant de la mensualité')
    apres_recep = fields.Monetary(string='Après réception')
    
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, readonly=True, states={'draft': [('readonly', False)]},
                                  default=lambda self: self.env.company.currency_id.id)
    project_comsumption_line = fields.One2many('project.comsumption.line', 'lead_id', string='Consommation',copy=True,auto_join=True)
    contrat_generated = fields.Binary(string='Contrat généré')
    contract_signed = fields.Boolean(string='Contrat signé')
    contract_signed_date = fields.Date(string='Date de signature du contrat', default=False)
    contract_validated = fields.Boolean(string='Contrat validé')
    contract = fields.Binary(string='Contrat')    

    power_level = fields.Float(string='Puissance à installée')
    autorise = fields.Boolean(string='Autorisation', default=False)
    typ_aut = fields.Selection([('anme','ANME'),('apia','APIA')],string="Type d'autorisation")
    config_projet = fields.Char(string="Configuration du projet")
    attachment_ids = fields.Many2many(
        'ir.attachment', 'lead_ooprtunity_attachment_rel',
        'name', 'attachment_id', string='Paiements', store=True, tracking=True)
    date_dep_tech = fields.Date(string='Date de dépôt du dossier technique')
    doc_tech = fields.Binary(string='Dossier Technique')
    payment_terms = fields.Text(string='Modalités de paiement', compute='_compute_payment_terms', store=True)
    date_prem_eche = fields.Date(string='Date de la première échéance')
    receptionne = fields.Boolean(string='Receptionné')
    pv_reception = fields.Binary(string='PV de réception')
    date_mise_serv = fields.Date(string='Date de mise en service')

    def valider_visite_tech(self):
        user = self.env.user
        if not user.has_group('crm_type_usage.chef_technique'):
            raise UserError(_("Vous n'avez pas les droits pour valider la visite technique."))
        if not self.visite_joint:
            raise UserError(_("Veuillez joindre le dossier technique avant de valider la visite technique."))
        self.write({'stage_id': 7,'context_crm': True})

    @api.depends('typ_paie', 'comptant', 'cred_steg', 'sub_anme', 'nb_mensualite', 'montant_mensualite', 'selected_order_id.amount_total')
    def _compute_payment_terms(self):
        for record in self:
            if not record.typ_paie or not record.selected_order_id:
                record.payment_terms = False
                continue
            
            if record.typ_paie == 'comptant':
                record.payment_terms = f"""<p>Paiement comptant:</p>
                <p>- Avance: {record.comptant} {record.currency_id.symbol}</p>
                <p>- Après réception: {record.apres_recep} {record.currency_id.symbol}</p>"""
            elif record.typ_paie == 'credit':
                record.payment_terms = f"""<p>Paiement en crédit:</p>
                <p>- Avance: {record.comptant} {record.currency_id.symbol}</p>
                <p>- Crédit STEG: {record.cred_steg} {record.currency_id.symbol}</p>
                <p>- Nombre de traites: {record.nb_mensualite}</p>
                <p>- Montant de la mensualité: {record.montant_mensualite} {record.currency_id.symbol}</p>"""
            elif record.typ_paie == 'mensuel':
                record.payment_terms = f"""<p>Paiement mensuel:</p>
                <p>- Nombre de mensualités: {record.nb_mensualite}</p>
                <p>- Montant par mensualité: {record.montant_mensualite} {record.currency_id.symbol}</p>
                <p>- Avance: {record.comptant} {record.currency_id.symbol}</p>"""

    def write(self, vals):
        for lead in self:
            _logger.info("vals %s", vals) 
            user = self.env.user
            if 'contract_validated' in vals and vals['contract_validated']:
                if not lead.contract_signed:
                    raise UserError(_("Veuillez importer le contrat avant de valider.")) 
                if vals['contract_validated']:
                    vals['stage_id'] = 4 #Gagné       
            if 'contract' in vals and (lead.user_id==user or user.has_group('crm_type_usage.chef_logistique')):
                if vals['contract']:
                    lead.contract_signed = True
                    lead.contract_signed_date = fields.Date.today()
                    vals['stage_id'] = 6
            _logger.info("vals %s", vals)

            if 'stage_id' in vals:
                if vals['stage_id'] == 2:
                    if not 'context_crm' in vals:
                        raise UserError(_("Veuillez générer une commande de vente avant de passer à l'état Proposition"))
                if vals['stage_id'] == 3:
                    if not 'context_crm' in vals:
                        raise UserError(_("Veuillez utiliser le bouton valider proposition pour passer à l'état Qualifié"))    
                if vals['stage_id'] == 5:
                    if not 'context_crm' in vals:
                        raise UserError(_("Veuillez générer le contrat avant de passer à l'état Signature du contrat"))
                if vals['stage_id'] == 6:
                    if not lead.contract_signed:
                        raise UserError(_("Veuillez signer le contrat avant de passer à l'état contrat signé"))
                if vals['stage_id'] == 4:
                    if not lead.contract_validated and not 'contract_validated' in vals:
                        raise UserError(_("Veuillez valider le contrat avant de passer à l'état Gagné"))
                if vals['stage_id'] == 7:
                    if not user.has_group('crm_type_usage.chef_technique'):
                        raise UserError(_("Vous n'avez pas les droits pour passer à l'état Visite technique"))
                if vals['stage_id'] == 8:
                    if not user.has_group('crm_type_usage.chef_technique'):
                        raise UserError(_("Vous n'avez pas les droits pour passer à l'état à livrer"))
                if vals['stage_id'] == 9:
                    if not user.has_group('crm_type_usage.chef_technique'):
                        raise UserError(_("Vous n'avez pas les droits pour passer à l'état livré"))
        return super().write(vals)
    
    #visite technique
    coord_gps=fields.Text(string="Coordonnées GPS")
    date_visite_tech=fields.Date(string="Date de la visite technique")
    district=fields.Many2one('res.country.state',string="District", domain="[('country_id', '=', country_id)]")
    sys_mont=fields.Selection([('toiture','Sur Toiture'),('sol','Au Sol'),('charpente','Sur Charpente'),('autres','Autres')],string="Système de montage(emplacement de linstallation)")
    inc_charp = fields.Float(string="Inclinaison de la charpente")
    risq_omb=fields.Selection([('oui','Oui'),('non','Non')],string="Risque d'ombrage")
    typ_compt=fields.Selection([('mono','Compteur Monophasé(Lumière)'),('tri','Compteur Triphasé(Force)')],string="Type de compteur" , tracking=True)
    cal_dis=fields.Text(string="Calibre du disjoncteur STEG" , tracking=True)
    photo_dis=fields.Binary(string="Photo du disjoncteur STEG" , tracking=True)
    photo_compteur=fields.Binary(string="Photo du compteur" , tracking=True)
    long_toit=fields.Float(string="Longueur de la toiture" , tracking=True)
    larg_toit=fields.Float(string="Largeur de la toiture" , tracking=True)
    dis_ac=fields.Float(string="Distance AC entre onduleur et compteur" , tracking=True)
    dis_dc=fields.Float(string="Distance DC Station et Onduleur" , tracking=True)
    dis_st_rg=fields.Float(string="Distance entre Station et Regard de terre" , tracking=True)
    empl_tech=fields.Text(string="Emplacement Local Technique" , tracking=True)
    typ_mat=fields.Selection([('alu','Aluminium'),('acier','Acier'),('autres','Autres')],string="Type Matériaux pour Structure" , tracking=True)
    typ_str=fields.Selection([('triangle','Triangle'),('trapeze','Trapèze'),('autres','Autres')],string="Type Structure" , tracking=True)
    haut_st=fields.Selection([('20cm','20cm'),('30cm','30cm'),('40cm','40cm'),('50cm','50cm'),('60cm','60cm'),('70cm','70cm'),('plus','PLUS')],string="Haut de la structure" , tracking=True)
    incli_struc= fields.Selection([('10','10°'),('15','15°'),('20','20°'),('30','30° recommandé'),('45','45°')],string="Inclinaison de la structure" , tracking=True)
    nb_niv_stru= fields.Selection([('1','1 niveau recommandé'),('2','2 niveaux'),('3','3 niveaux'),('autres','Autres')],string="Nombre de niveaux de structure" , tracking=True)
    comments=fields.Text(string="Commentaires")
    context_crm = fields.Boolean(string="context", default=False)
    selected_order_id = fields.Many2one('sale.order', string='Commande sélectionnée')
    phot_toit = fields.Binary(string="Photo du toit")
    phot_emp_tech = fields.Binary(string="Photo de l'emplacement de la station")
    panel = fields.Many2one('product.product', string='Panneaux', domain="[('categ_id.name', '=', 'PANNEAUX')]")
    inverter = fields.Many2one('product.product', string='Onduleur',domain="[('categ_id.name', '=', 'ONDULEURS')]")
    nb_panel = fields.Integer(string="Nombre de panneaux")
    nb_inverter = fields.Integer(string="Nombre d'onduleurs")
    bc_tele = fields.Binary(string="Bon de Commande")
    available_order_id = fields.Many2one(
        'sale.order',
        string="Commandes disponibles",
        domain="[('id', 'in', order_ids)]"  # Filters options based on order_ids
    )
    
    
    def valider_propo(self):
        """ if not self.available_order_id:
            raise UserError(_("Please select an order before validating the proposal.")) """
        if not self.selected_order_id:
            raise UserError(_("Veuillez joindre le bon de commande avant de valider la proposition."))
        # Verify if user has technical manager rights
        user = self.env.user
        if not user.has_group('crm_type_usage.chef_technique'):
            raise UserError(_("You must be a technical manager to validate proposals."))
        self.write({
            'stage_id': 3,
            'context_crm': True,
            #'selected_order_id': self.available_order_id.id
        })

    def _extract_power_from_name(self, name, is_inverter=False):
        """Extract power value from product name"""
        if not name:
            return 0
        import re  # Ensure re is imported
        if is_inverter:
            power_match = re.search(r'(\d+(?:\.\d+)?)\s*[Kk][Ww]', name)
            if power_match:
                power = float(power_match.group(1))
                return int(power) if power.is_integer() else power
        else:
            power_match = re.search(r'(\d+)\s*[Ww][Cc]', name)
            if power_match:
                power = float(power_match.group(1))
                return int(power) if power.is_integer() else power
        return 0

    def _extract_brand_from_name(self, name, is_inverter=False):
        """Extract brand name by removing power value from product name"""
        if not name:
            return ''
        import re
        if is_inverter:
            # Remove power value with KW unit
            brand = re.sub(r'\d+(?:\.\d+)?\s*[Kk][Ww]', '', name)
        else:
            # Remove power value with WC unit
            brand = re.sub(r'\d+\s*[Ww][Cc]', '', name)
        # Clean up any extra whitespace and return
        return brand.strip()

    def _get_contract_data(self):
        """Prepare data for the contract template"""
        self.ensure_one()
        """ if not self.selected_order_id:
            raise UserError(_("Veuillez sélectionner une commande avant de générer le contrat.")) """
            
        # Extract panel and inverter power from names
        panel_power = self._extract_power_from_name(self.panel.name if self.panel else '')
        inverter_power = self._extract_power_from_name(self.inverter.name if self.inverter else '', is_inverter=True)
        # Extract panel and inverter brands using the dedicated method
        panel_brand = self._extract_brand_from_name(self.panel.name if self.panel else '')
        inverter_brand = self._extract_brand_from_name(self.inverter.name if self.inverter else '', is_inverter=True)
        # Prepare payment terms based on typ_paie
        payment_terms = f"""Le client s'engage à verser un montant de {self.engagement} {self.currency_id.symbol} à la première parti comme suit:"""
        if self.typ_paie == 'comptant':
            payment_terms += f"<ul>"
            if self.comptant !=0:
                payment_terms += f"<li> Un montant de {self.comptant} (TTC) à la signature du contrat</li>"
            if self.rec_equi !=0:
                payment_terms += f"<li> Un montant de {self.rec_equi} (TTC) à la réception des équipements sur site.</li>"
            if self.fin_trav !=0:
                payment_terms += f"<li> Un montant de {self.fin_trav} (TTC) à la  fin des travaux.</li>"
            if self.apr_rec != 0:
                payment_terms += f"<li> Un montant de {self.apr_rec} (TTC) après réception STEG.</li></ul>"
            if self.apres_recep !=0:
                payment_terms += f"<li> Un montant de {self.apres_recep} (TTC) après réception.</li></ul>"
        else:
             payment_terms = f"<p>Un montant de {self.expected_revenue} TTC sera réglé à l’achèvement des travaux en {self.nb_mensualite} mensualités justifiées par des traites bancaires soit {self.comptant} {self.currency_id.symbol} par mois.</p>"
        
        _logger.info("payment_terms %s", payment_terms)
        return {
            'lead': self,
            'order': self.selected_order_id,
            'company': self.company_id,
            'partner': self.partner_id,
            'counter_id': self.counter_id.name if self.counter_id else '',
            'payment_terms': payment_terms,
            'panel_quantity': self.nb_panel,
            'panel_brand': panel_brand,
            'panel_power': panel_power,
            'panel': self.panel.name if self.panel else '',
            'inverter': self.inverter.name if self.inverter else '',
            'inverter_quantity': self.nb_inverter,
            'inverter_brand': inverter_brand,
            'inverter_power': inverter_power,
            'steg_credit': self.cred_steg if self.cred_steg else 0,
            'self_financing': self.comptant if self.comptant else 0,
            'cash_contract_amount': self.selected_order_id.amount_total if self.typ_paie == 'comptant' else '.....................',
        }

    def _get_report_data(self):
        """Prepare data for the report template"""
        self.ensure_one()
        contract_data = self._get_contract_data()
        return {
            'doc_ids': self.ids,
            'doc_model': 'crm.lead',
            'docs': self,
            'data': contract_data
        }

    def generate_contrat(self):
        """Generate and store the contract PDF"""
        self.ensure_one()
        if not self.selected_order_id:
            raise UserError(_("Veuillez sélectionner une commande de vente avant de générer le contrat."))
        user = self.env.user
        if user != self.user_id:
            raise UserError(_("Sauf le commerciale %s peut générer le contrat.",self.user_id.name))
        
        if not self.counter_id or not self.power_level or not self.partner_id.cin:
            raise UserError(_("Veuillez sélectionner un compteur et un niveau de puissance et numéro de CIN avant de générer le contrat."))
        # Generate PDF using report template
        report = self.env.ref('crm_type_usage.report_installation_contract')
        pdf_content = report._render_qweb_pdf([self.id], data=self._get_report_data())[0]
        
        # Store the generated PDF in contrat_generated field
        self.write({
            'contrat_generated': base64.b64encode(pdf_content),
            'stage_id': 5,
            'context_crm': True
        })

        return True


    tel = fields.Char(string="Téléphone",related="partner_id.phone")
    steg_exis = fields.Boolean(string="Existance réseau STEG")
    surface_terrain = fields.Float(string="Surface du terrain à irriguer")
    prep_terrain = fields.Selection([('oui','Oui'),('non','Non')],string="Préparation du terrain",default='non')
    fix_sol = fields.Selection([('dalle','Dalle'),('chainage','Chainage')],string="Fixation du sol",default='dalle')
    typ_projet = fields.Selection([('anme','ANME'),('apia','APIA'),('non_aut','non Autorisé')],string="Type de projet")
    fich_tech_sonde = fields.Binary(string="Fiche technique de la sondage")
    pomp_exist = fields.Selection([('oui','Oui'),('non','Non')],string="Existence de la pompe")
    puissance_pompe = fields.Selection([('2.2','2.2 cv'),('3','3 cv'),('4','4 cv'),('5.5','5.5 cv'),('7.5','7.5 cv'),('10','10 cv'),('12.5','12.5 cv'),('15','15 cv'),('20','20 cv'),('25','25 cv'),('30','30 cv')],string="Puissance de la pompe")
    fich_tech_pompe = fields.Binary(string="Fiche technique de la pompe")
    prof_immer = fields.Float(string="Profondeur d'immersion")
    typ_ref = fields.Selection([('direct','DIRECTE'),('bassin','Bassin')],string="Type de refoulement")
    dis_ref = fields.Float(string="Distance de refoulement")
    haut_res = fields.Float(string="Haut de réservoir")
    dist_puit_bassin = fields.Float(string="Distance entre le puits et le bassin")
    dis_panneau_loc = fields.Float(string="Distance entre les panneaux et le local technique")
    dis_loc_puit = fields.Float(string="Distance entre le local technique et le puits")
    emp_loc_tech = fields.Text(string="Emplacement du local technique")
    sys_comp = fields.Boolean(string="Existance du système de compensation")
    #partie mécanique
    nb_stru = fields.Integer(string="Nombre de structure")
    typ_projet = fields.Selection([('anme','ANME'),('apia','APIA'),('non_aut','non Autorisé')],string="Type de projet")
    typ_mat_stru = fields.Selection([('acier','Acier'),('aluminium','Aluminium')],string="Type de matériaux pour structure")
    typ_stru = fields.Selection([('triangle','Triangle'),('trapeze','Trapèze')],string="Type de structure")
    haut_trap = fields.Selection([('30','30cm'),('40','40cm'),('50','50cm'),('60','60cm'),('70','70cm'),('80','80cm'),('90','90cm')],string="Haut de trapèze")
    inc_stru = fields.Selection([('15','15°'),('20','20°non Autorise'),('30','30° ANME-APIA')])
    nb_niv_stru = fields.Selection([('1','1 recommendé'),('2','2'),('3','3 à éviter')],string="Nombre de niveau de structure")
    visite_joint = fields.Binary(string="Svp Joindre la visite technique")
