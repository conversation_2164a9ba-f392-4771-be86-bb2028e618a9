<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <report
        id="report_installation_contract"
        model="crm.lead"
        string="Contrat d'Installation"
        report_type="qweb-pdf"
        name="crm_usage_comp.report_installation_contract_document"
        file="crm_usage_comp.report_installation_contract_document"
        paperformat="base.paperformat_euro"
        print_report_name="'Contrat_%s' % object.name"
    />

        <template id="report_installation_contract_document">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.external_layout">
                        <t t-set="data" t-value="o._get_report_data()"/>
                                  <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            font-size: 16px!important; /* Increased from default */
            width: 100%;
        }
        .page {
            width: 210mm;
            min-height: auto;
            margin: 0;
            padding: 5mm;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            page-break-after: always;
        }
        .page:last-child {
            page-break-after: avoid;
        }
        .content-wrapper {
            flex-grow: 1;
            position: relative;
            z-index: 1;
        }


        .contract-title {
            text-align: center;
            font-weight: bold;
            text-decoration: underline;
            font-size: 22px!important; /* Increased from 16px */
            margin-top: -10px;
            color: blue;
            font-style: italic;
            margin-bottom: 30px;
        }
        h3 {
            font-weight: bold;
            font-size: 18px; /* Increased from 14px */
            margin-top: 20px;
            margin-bottom: 10px;
        }
        p, li {
            font-size: 14px; /* Increased from 12px */
            text-align: justify;
        }
        ul {
            list-style: none; /* Remove default bullets */
            padding-left: 20px;
        }
        ul li::before {
            content: "•"; /* Add custom bullet */
            color: black;
            font-weight: bold;
            display: inline-block;
            width: 1em;
            margin-left: -1em;
        }
        .signature-section {
            margin-top: 50px;
            margin-bottom: 30px;
            display: table;
            width: 100%;
            text-align: center; /* Add this */
        }
        .signature-left {
            display: table-cell;
            width: 200px;
            text-align: left;
        }
        .signature-right {
            display: table-cell;
            width: 200px;
            text-align: right;
        }
        .signature-line {
            border-top: 1px solid #000;
            margin-bottom: 5px;
            width: 200px;
        }
        .signature-text {
            text-align: center;
            font-size: 14px; /* Increased from 12px */
            width: 200px;
        }
        .bold {
            font-weight: bold;
            display: inline;
        }
        .watermark {
             position: absolute;
             top: 50%;
             left: 50%;
             transform: translate(-50%, -50%) rotate(-45deg);
             -webkit-transform: translate(-50%, -50%) rotate(-45deg);
             -moz-transform: translate(-50%, -50%) rotate(-45deg);
             -ms-transform: translate(-50%, -50%) rotate(-45deg);
             -o-transform: translate(-50%, -50%) rotate(-45deg);
             font-size: 120px; /* Increased from 100px */
             font-family: "Times New Roman", Times, serif;
             color: rgba(151, 170, 202, 0.2); /* More transparent */
             z-index: 0;
             pointer-events: none;
             white-space: nowrap;
             letter-spacing: 5px;
             opacity: 0.2; /* Additional transparency */
             filter: alpha(opacity=20); /* For IE */
         }

    </style>
                        <div class="page">
                            <div class="watermark">SOLEVO BY SHZ</div>
                            
                            <div class="content-wrapper">
                                <div class="contract-title">CONTRAT DE RÉALISATION DES TRAVAUX</div>

                                <p><span style="font-weight: bold;font-size: 20px!important;">Entre les soussignés :</span></p>

                                <p style="font-size: 16px!important;">1. <span style="font-weight: bold;font-size: 20px!important;">La Société SOLEVO BY SHZ</span>, une société à responsabilité limitée, ayant
                                son siège social au Rue cheikh El Neifer, Immeuble BAYA, Appartement n°7, Ain
                                Zaghouan, Tunis, identifiée sous le numéro B2493592007 et son identification fiscale
                                1031949/B/A/M000, représentée par son représentant légal, Monsieur Zouheir Ben
                                Taher Houidi, citoyen tunisien, détenteur de la carte d'identité nationale n° 01085627,
                                délivrée à Tunis le 06 /09/2021, résidant au Rue Bouzyane, Impasse 62, Villa 8, Sfax.
                                (ci-après dénommé « le Premier Parti »).</p>

                                <p style="font-size: 16px!important;">2. <span style="font-weight: bold;font-size: 20px!important;">La Deuxième parti</span>, représentée par Mr/Mme <span t-field="o.partner_id.name"/>, détenteur de la
                                carte d'identité nationale n° <t t-if="o.partner_id.cin"><span t-field="o.partner_id.cin"/></t><t t-else="">.................................</t>, délivrée à Tunis le <t t-if="o.partner_id.x_adress_cin"><span t-field="o.partner_id.x_adress_cin"/></t><t t-else="">.................................</t>.
                                <br/>
                                D'autre part,</p>

                                <p><span style="font-weight: bold;font-size: 20px!important;">Il a été convenu et arrêté ce qui suit :</span></p>

                                <h3>Article 1 : Objet du contrat</h3>

                                <p style="font-size: 16px!important;">Le présent contrat a pour objet la conception, la fourniture et l'installation d'une <span style="font-weight: bold;font-size: 20px!important;"> centrale
                                photovoltaïque</span> d'une capacité de <span t-field="o.power_level"/> kWc, située à <t t-if="o.partner_id.x_adress_fact"><span t-field="o.partner_id.x_adress_fact"/></t><t t-else="">.................................</t>
                                connectée au réseau de la Société Tunisienne de l'Électricité et du Gaz (STEG), sous référence <span t-field="o.counter_id"/>.</p>

                                <p style="font-size: 16px!important;">Le montant total des travaux s'élève à <span t-field="o.selected_order_id.amount_total"/> <span t-field="o.currency_id.symbol"/> toutes taxes comprises (TTC).</p>

                                <h3>Article 2 : Composants de l'installation</h3>

                                <p style="font-size: 16px!important;">La centrale photovoltaïque comprend :</p>
                                <ul>
                                    <li style="font-size: 16px!important;">
                                        <span style="font-size: 16px!important;" t-esc="data['data'].get('panel_quantity')"/> panneaux solaires de marque <span t-esc="data['data'].get('panel')"/> d'une puissance unitaire de <t t-esc="data['data'].get('panel_power')"/> Wc
                                    </li>
                                    <li style="font-size: 16px!important;">
                                        <span style="font-size: 16px!important;" t-esc="data['data'].get('inverter_quantity')"/> onduleur(s) de marque <span t-esc="data['data'].get('inverter')"/> d'une puissance de <t t-esc="data['data'].get('inverter_power')"/> kW
                                    </li>
                                    <li style="font-size: 16px!important;">Structure de montage, câblage et équipements nécessaires au bon fonctionnement de l'installation.</li>
                                </ul>

                                <h3>Article 3 : Type de contrat STEG</h3>
                               <ul>
  <t t-if="(data['data'].get('steg_credit',0) > 0)">
    <li style="font-size: 16px!important;">
      Crédit STEG :
      <span t-esc="data['data'].get('steg_credit','.........')"/> 
      avec autofinancement 
      <span t-esc="data['data'].get('self_financing', '.........')"/>(TTC).
    </li>
  </t>

  <t t-else="">
    <li style="font-size: 16px!important;">
      Contrat comptant d'un montant 
      <span t-esc="o.expected_revenue"/>(TTC).
    </li>
  </t>
</ul>
                            </div>
                        </div>

                        <div class="page">
                            <div class="watermark">SOLEVO BY SHZ</div>

                            <div class="content-wrapper">
                                <h3>Article 4 : Conditions de paiement</h3>
                                <span t-raw="data['data'].get('payment_terms')"/>


                                <h3>Article 5 : Garantie et service après-vente</h3>

                                <p style="font-size: 16px!important;">Le Premier Parti garantit :</p>
                                <ul>
                                        <li style="font-size: 16px!important;"><span class="bold">Les panneaux solaires :</span> Garantie de performance d'au moins 80 % de leur capacité pendant ....... ans.</li>
                                        <li style="font-size: 16px!important;"><span class="bold">Les onduleurs :</span> garantie de ...... ans contre tout dysfonctionnement.</li>
                                </ul>
                                <p style="font-size: 16px!important;">Toute intervention hors garantie ou due à une mauvaise utilisation par la Deuxième Partie sera facturée selon les tarifs en vigueur</p>
                                <h3 style="display: inline;">Article 6 : </h3>
                                <span style="font-size: 16px!important;">En cas de défaillance technique, la société s'engage à effectuer l'inspection et la réparation dans un délai maximal de 72 heures à compter de la notification par tout moyen laissant une trace écrite, sauf en cas de force majeure.</span>
                                <br/>
                                <h3 style="display: inline;">Article 7 :</h3>
                                <span style="font-size: 16px!important;">La centrale reste la propriété du Premier Parti tant que le paiement du montant des travaux stipulé à l'Article 1 n'a pas été effectué par le Deuxième parti.</span>
                                <br/>
                                <h3 style="display: inline;">Article 8 :</h3>
                                <span style="font-size: 16px!important;">La société ne sera pas responsable des dommages ou de la détérioration de la centrale photovoltaïque résultant de cas de force majeure ou de dommages dus à un vol ou à un incendie (sauf causé par la centrale elle-même).</span>
                                <br/>
                                <h3 style="display: inline;">Article 9 :</h3>
                                <span style="font-size: 16px!important;">Si les travaux nécessitent une extension du projet convenu à l'Article 2, y compris l'ajout de travaux ou d'équipements supplémentaires, les prix seront facturés selon les tarifs en vigueur.</span>
                                <br/>
                                <h3 style="display: inline;">Article 10 :</h3>
                                <span style="font-size: 16px!important;">Les tribunaux de Sfax sont seuls compétents pour régler les litiges pouvant survenir lors de l'exécution du présent contrat.</span>
                       <div class="signature-section">
                                    <div class="signature-left">
                                        <div class="signature-line"/>
                                        <div class="signature-text">Représentant du Premier Partie</div>
                                        <img t-attf-src="data:image/jpg;base64,{{ o.company_id.signature }}" align="center" width="200" style="margin:0px;padding-top:0px;"/>
                                    </div>

                                    <div style="display: table-cell; width: 100%;"/> <!-- Spacer -->

                                    <div class="signature-right">
                                        <div class="signature-line"/>
                                        <div class="signature-text">Représentant du Deuxième Partie</div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>