from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging
import base64
import re
import fitz

_logger = logging.getLogger(__name__)
class CrmLead(models.Model):
    _inherit = 'crm.lead'

    def _get_contract_data(self):
        """Prepare data for the contract template"""
        self.ensure_one()
        panel_quantity =0
        if not self.selected_order_id:
            raise UserError(_("Veuillez sélectionner une commande avant de générer le contrat."))
        # Fetch the panel product from the selected order lines where category is "PANNEAUX"
        panel = ""
        if self.selected_order_id:
            panel_line = self.selected_order_id.order_line.filtered(lambda l: l.product_id.categ_id.name == "PANNEAUX")
            if panel_line:
                panel = panel_line[0].product_id
                panel_quantity = panel_line[0].product_uom_qty
        # Extract panel and inverter power from names
        panel_power = self._extract_power_from_name(panel.name if panel else '') 
        panel_brand = self._extract_brand_from_name(panel.name if panel else '')
        _logger.info(f"Panel Power: {panel_power}, Panel Brand: {panel_brand}")



        # Fetch the inverter product(s) from the selected order lines where category is "ONDULEURS"
        inverter_lines = self.selected_order_id.order_line.filtered(lambda l: l.product_id.categ_id.name == "ONDULEURS")
        inverters = []
        inverter_text = ""
        for inverter_line in inverter_lines:
            inverter_name = inverter_line.product_id.name
            inverter_power = self._extract_power_from_name(inverter_name, is_inverter=True)
            inverter_brand = self._extract_brand_from_name(inverter_name, is_inverter=True)
            inverter_quantity = inverter_line.product_uom_qty
            _logger.info(f"Inverter Brand: {inverter_brand}, Inverter Power: {inverter_power}, Inverter Quantity: {inverter_quantity}")
            inverter_text += f"""
                <li style="font-size: 16px!important;">
                    {inverter_quantity} Onduleur(s) de marque {inverter_brand} d'une puissance de {inverter_power} kW
                </li>
                """
            inverters.append((inverter_brand, inverter_power, inverter_quantity))

        # Prepare payment terms based on typ_paie
        payment_terms = f"""Le client s'engage à verser un montant de {self.engagement} {self.currency_id.symbol} à la première parti comme suit:"""
        if self.typ_paie == 'comptant':
            payment_terms += f"<ul>"
            if self.comptant !=0:
                payment_terms += f"<li> Un montant de {self.comptant} (TTC) à la signature du contrat</li>"
            if self.rec_equi !=0:
                payment_terms += f"<li> Un montant de {self.rec_equi} (TTC) à la réception des équipements sur site.</li>"
            if self.fin_trav !=0:
                payment_terms += f"<li> Un montant de {self.fin_trav} (TTC) à la  fin des travaux.</li>"
            if self.apr_rec != 0:
                payment_terms += f"<li> Un montant de {self.apr_rec} (TTC) après réception STEG.</li></ul>"
            if self.apres_recep !=0:
                payment_terms += f"<li> Un montant de {self.apres_recep} (TTC) après réception.</li></ul>"
        else:
            if self.engagement > 0:
                payment_terms = f"<p>Un montant de {self.expected_revenue} TTC sera réglé à l’achèvement des travaux en {self.nb_mensualite} mensualités justifiées par des traites bancaires soit {self.comptant} {self.currency_id.symbol} par mois.</p>"


        _logger.info("payment_terms %s", payment_terms)
        _logger.info("inverter_text origin %s", inverter_text)
        return {
            'lead': self,
            'order': self.selected_order_id,
            'company': self.company_id,
            'partner': self.partner_id,
            'counter_id': self.counter_id.name if self.counter_id else '',
            'payment_terms': payment_terms,
            'panel_quantity': panel_quantity,
            'panel_brand': panel_brand,
            'panel_power': panel_power,
            'inverter_text': inverter_text,
            'steg_credit': self.cred_steg if self.cred_steg else 0,
            'self_financing': self.comptant if self.comptant else 0,
            'cash_contract_amount': self.selected_order_id.amount_total if self.typ_paie == 'comptant' else '.....................',
        }
    
    def generate_contrat(self):
        """Generate and store the contract PDF"""
        self.ensure_one()
        if not self.selected_order_id:
            raise UserError(_("Veuillez sélectionner une commande de vente avant de générer le contrat."))
        user = self.env.user
        if user != self.user_id:
            raise UserError(_("Sauf le commerciale %s peut générer le contrat.",self.user_id.name))
        
        if not self.counter_id or not self.power_level or not self.partner_id.cin:
            raise UserError(_("Veuillez sélectionner un compteur et un niveau de puissance et numéro de CIN avant de générer le contrat."))
        # Generate PDF using report template
        report = self.env.ref('crm_usage_comp.report_installation_contract')
        pdf_content = report._render_qweb_pdf([self.id], data=self._get_report_data())[0]
        
        # Store the generated PDF in contrat_generated field
        self.write({
            'contrat_generated': base64.b64encode(pdf_content),
            'stage_id': 5,
            'context_crm': True
        })
    
    contrat_steg_generated= fields.Binary(string="Contrat STEG Généré")

    
    def generate_contrat_steg_cred(self,input_file):
        # Use relative path to access the static folder
        import os
        module_path = os.path.dirname(os.path.dirname(__file__))
        full_path = os.path.join(module_path, 'static', input_file)
        
        # Create a temporary file path for the output
        import tempfile
        output_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False).name
        
        doc = fitz.open(full_path)
        
        page = doc[0]
        #name
        page.insert_text(
            (115, 200),
            self.partner_id.name,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #num cin
        page.insert_text(
            (80, 210),
            self.partner_id.cin,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #date cin
        page.insert_text(
            (370, 210),
            str(self.partner_id.x_date_cin) if self.partner_id.x_date_cin else "",
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #adresse
        page.insert_text(
            (110, 222),
            self.partner_id.street,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #telephone
        page.insert_text(
            (115, 243),
            self.partner_id.phone,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #num devis
        page.insert_text(
            (85, 380),
            self.selected_order_id.name,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #date devis
        page.insert_text(
            (165, 380),
            self.selected_order_id.date_order.strftime('%d/%m/%Y'),
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #nb kw
        page.insert_text(
            (305, 355),
            str(self.power_level) if self.power_level else "",
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )

        if self.counter_id and self.counter_id.name and len(self.counter_id.name) == 9:
            positions = [90, 135, 170, 220, 270, 350, 380, 430, 500]
            
            for x, char in zip(positions, self.counter_id.name):
                page.insert_text(
                    (x, 320),
                    char,
                    fontsize=10,
                    fontname="Times-Roman",
                    fill=(0, 0, 0)  # black
                )

        #page 1/5
        page = doc[1]
        #name
        page.insert_text(
            (70, 370),
            self.partner_id.name,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #adresse
        page.insert_text(
            (90, 395),
            self.partner_id.street,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )


        page = doc[5]
        #adresse
        page.insert_text(
            (90, 200),
            self.partner_id.street,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #compteur
        page.insert_text(
            (215, 230),
            self.counter_id.name,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        panel_quantity =0
        if not self.selected_order_id:
            raise UserError(_("Veuillez sélectionner une commande avant de générer le contrat."))
        # Fetch the panel product from the selected order lines where category is "PANNEAUX"
        panel = ""
        if self.selected_order_id:
            panel_line = self.selected_order_id.order_line.filtered(lambda l: l.product_id.categ_id.name == "PANNEAUX")
            if panel_line:
                panel = panel_line[0].product_id
                panel_quantity = panel_line[0].product_uom_qty
        # Extract panel and inverter power from names
        _logger.info("panel name %s",panel.name if panel else '')

        panel_power = self._extract_power_from_name(panel.name if panel else '') 
        _logger.info("panel power %s",panel_power)
        _logger.info("panel quantity %s",panel_power)

        #nb panneau
        page.insert_text(
            (220, 273),
            str(panel_quantity),
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #puissance panneau
        page.insert_text(
            (65, 307),
            str(panel_power),
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #nb kw
        page.insert_text(
            (110, 320),
            str(self.power_level) if self.power_level else "",
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        doc.save(output_file)
        return output_file
    def generate_contrat_steg_compt(self,input_file):
        # Use relative path to access the static folder
        import os
        module_path = os.path.dirname(os.path.dirname(__file__))
        full_path = os.path.join(module_path, 'static', input_file)
        
        # Create a temporary file path for the output
        import tempfile
        output_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False).name
        
        doc = fitz.open(full_path)
        page = doc[0]
        page.insert_text(
            (70,400),
            self.partner_id.name,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #adresse
        page.insert_text(
            (90, 415),
            self.partner_id.street,
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )

        #page 5/5
        page = doc[4]
        #référence
        page.insert_text(
            (270, 130),
            str(self.counter_id.name) if self.counter_id else "",
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        #nb kw
        page.insert_text(
            (70, 240),
            str(self.power_level) if self.power_level else "",
            fontsize=10,
            fontname="Times-Roman",
            fill=(0, 0, 0)  # black
        )
        doc.save(output_file)
        return output_file


    def generate_contrat_steg(self):
        self.ensure_one()
        # Determine which PDF to use based on payment type
        if self.typ_paie == 'comptant':
            input_file = "contrat_compt.pdf"
            output_file = self.generate_contrat_steg_compt(input_file)
        else:
            if self.power_level == 2:
                input_file = "CONTRAT_credit_2kw.pdf"
            else:
                input_file = "CONTRAT_credit_3kw.pdf"
            output_file = self.generate_contrat_steg_cred(input_file)
        
        # Read the modified PDF and save it to contrat_generated field
        with open(output_file, 'rb') as pdf_file:
            pdf_content = pdf_file.read()
            self.write({
                'contrat_steg_generated': base64.b64encode(pdf_content),
            })
        
        # Clean up the temporary file
        import os
        try:
            os.unlink(output_file)
        except:
            pass
        
        return True



