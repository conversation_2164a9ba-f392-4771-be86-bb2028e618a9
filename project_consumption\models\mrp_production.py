from odoo import fields, models, api

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    opportunity_id = fields.Many2one(
        related='procurement_group_id.sale_id.opportunity_id',
        string='Opportunity',
        store=True
    )

    # Visite Technique fields - Section 1: Identification Client
    coord_gps = fields.Text(
        related='opportunity_id.coord_gps',
        string='Coordonnées GPS',
        readonly=True
    )
    date_visite_tech = fields.Date(
        related='opportunity_id.date_visite_tech',
        string='Date de la visite technique',
        readonly=True
    )
    district = fields.Many2one(
        related='opportunity_id.district',
        string='District',
        readonly=True
    )

    # Section 2: Informations
    sys_mont = fields.Selection(
        related='opportunity_id.sys_mont',
        string='Système de montage',
        readonly=True
    )
    inc_charp = fields.Float(
        related='opportunity_id.inc_charp',
        string='Inclinaison de la charpente',
        readonly=True
    )
    risq_omb = fields.Selection(
        related='opportunity_id.risq_omb',
        string='Risque d\'ombrage',
        readonly=True
    )

    # Section 3: Logistique
    typ_compt = fields.Selection(
        related='opportunity_id.typ_compt',
        string='Type de compteur',
        readonly=True
    )
    cal_dis = fields.Text(
        related='opportunity_id.cal_dis',
        string='Calibre du disjoncteur STEG',
        readonly=True
    )
    photo_dis = fields.Binary(
        related='opportunity_id.photo_dis',
        string='Photo du disjoncteur STEG',
        readonly=True
    )
    photo_compteur = fields.Binary(
        related='opportunity_id.photo_compteur',
        string='Photo du compteur',
        readonly=True
    )

    # Section 4: Partie Electrique
    long_toit = fields.Float(
        related='opportunity_id.long_toit',
        string='Longueur de la toiture',
        readonly=True
    )
    larg_toit = fields.Float(
        related='opportunity_id.larg_toit',
        string='Largeur de la toiture',
        readonly=True
    )
    phot_toit = fields.Binary(
        related='opportunity_id.phot_toit',
        string='Photo du toit',
        readonly=True
    )
    dis_ac = fields.Float(
        related='opportunity_id.dis_ac',
        string='Distance AC entre onduleur et compteur',
        readonly=True
    )
    dis_dc = fields.Float(
        related='opportunity_id.dis_dc',
        string='Distance DC Station et Onduleur',
        readonly=True
    )
    dis_st_rg = fields.Float(
        related='opportunity_id.dis_st_rg',
        string='Distance entre Station et Regard de terre',
        readonly=True
    )
    empl_tech = fields.Text(
        related='opportunity_id.empl_tech',
        string='Emplacement Local Technique',
        readonly=True
    )
    phot_emp_tech = fields.Binary(
        related='opportunity_id.phot_emp_tech',
        string='Photo de l\'emplacement de la station',
        readonly=True
    )

    # Section 5: Partie Mécanique
    typ_mat = fields.Selection(
        related='opportunity_id.typ_mat',
        string='Type Matériaux pour Structure',
        readonly=True
    )
    typ_str = fields.Selection(
        related='opportunity_id.typ_str',
        string='Type Structure',
        readonly=True
    )
    haut_st = fields.Selection(
        related='opportunity_id.haut_st',
        string='Haut de la structure',
        readonly=True
    )
    incli_struc = fields.Selection(
        related='opportunity_id.incli_struc',
        string='Inclinaison de la structure',
        readonly=True
    )
    nb_niv_stru = fields.Selection(
        related='opportunity_id.nb_niv_stru',
        string='Nombre de niveaux de structure',
        readonly=True
    )
    comments = fields.Text(
        related='opportunity_id.comments',
        string='Commentaires',
        readonly=True
    )

    # Visite Technique
    visite_joint = fields.Binary(
        related='opportunity_id.visite_joint',
        string='Svp Joindre la visite technique',
        readonly=True
    )

    # Installation type
    installation_type = fields.Selection(
        related='opportunity_id.installation_type',
        string='Type d\'installation',
        readonly=True
    )

    # Pompage specific fields
    tel = fields.Char(
        related='opportunity_id.tel',
        string='Téléphone',
        readonly=True
    )
    steg_exis = fields.Boolean(
        related='opportunity_id.steg_exis',
        string='Existance réseau STEG',
        readonly=True
    )
    surface_terrain = fields.Float(
        related='opportunity_id.surface_terrain',
        string='Surface du terrain à irriguer',
        readonly=True
    )
    prep_terrain = fields.Selection(
        related='opportunity_id.prep_terrain',
        string='Préparation du terrain',
        readonly=True
    )
    fix_sol = fields.Selection(
        related='opportunity_id.fix_sol',
        string='Fixation du sol',
        readonly=True
    )
    typ_projet = fields.Selection(
        related='opportunity_id.typ_projet',
        string='Type de projet',
        readonly=True
    )
    fich_tech_sonde = fields.Binary(
        related='opportunity_id.fich_tech_sonde',
        string='Fiche technique de la sondage',
        readonly=True
    )

    # Partie Electrique - Pompe
    pomp_exist = fields.Selection(
        related='opportunity_id.pomp_exist',
        string='Existence de la pompe',
        readonly=True
    )
    puissance_pompe = fields.Selection(
        related='opportunity_id.puissance_pompe',
        string='Puissance de la pompe',
        readonly=True
    )
    fich_tech_pompe = fields.Binary(
        related='opportunity_id.fich_tech_pompe',
        string='Fiche technique de la pompe',
        readonly=True
    )

    # Sondage
    prof_immer = fields.Float(
        related='opportunity_id.prof_immer',
        string='Profondeur d\'immersion',
        readonly=True
    )
    typ_ref = fields.Selection(
        related='opportunity_id.typ_ref',
        string='Type de refoulement',
        readonly=True
    )
    dis_ref = fields.Float(
        related='opportunity_id.dis_ref',
        string='Distance de refoulement',
        readonly=True
    )
    haut_res = fields.Float(
        related='opportunity_id.haut_res',
        string='Haut de réservoir',
        readonly=True
    )
    dist_puit_bassin = fields.Float(
        related='opportunity_id.dist_puit_bassin',
        string='Distance entre le puits et le bassin',
        readonly=True
    )

    # Local Technique
    dis_panneau_loc = fields.Float(
        related='opportunity_id.dis_panneau_loc',
        string='Distance entre les panneaux et le local technique',
        readonly=True
    )
    dis_loc_puit = fields.Float(
        related='opportunity_id.dis_loc_puit',
        string='Distance entre le local technique et le puits',
        readonly=True
    )
    emp_loc_tech = fields.Text(
        related='opportunity_id.emp_loc_tech',
        string='Emplacement du local technique',
        readonly=True
    )
    sys_comp = fields.Boolean(
        related='opportunity_id.sys_comp',
        string='Existance du système de compensation',
        readonly=True
    )

    # Partie Mécanique - Pompage
    nb_stru = fields.Integer(
        related='opportunity_id.nb_stru',
        string='Nombre de structure',
        readonly=True
    )
    typ_mat_stru = fields.Selection(
        related='opportunity_id.typ_mat_stru',
        string='Type Matériaux pour Structure',
        readonly=True
    )
    typ_stru = fields.Selection(
        related='opportunity_id.typ_stru',
        string='Type Structure',
        readonly=True
    )
    haut_trap = fields.Selection(
        related='opportunity_id.haut_trap',
        string='Haut de trapèze',
        readonly=True
    )
    inc_stru = fields.Selection(
        related='opportunity_id.inc_stru',
        string='Inclinaison de la structure',
        readonly=True
    )