<odoo>
    <data>
        <record id="project_project_form_view" model="ir.ui.view">
            <field name="name">project.project.form</field>
            <field name="model">project.project</field>
            <field name="inherit_id" ref="project.edit_project"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="dossier Technique" name="Dossier Technique">
                        <group >
                            <field name="dossier_tech"/>
                            <field name="date_dep_tech"/>
                        </group>
                    </page>
                    <page string="Pré-facturation">
                        <header>
                            <button name="action_prefill_preinvoice" type="object" string="Pré-remplir depuis la Commande" class="oe_highlight"/>
                        </header>
                        <field name="preinvoice_line_ids">
                            <tree editable="bottom">
                                <field name="product_id"/>
                                <field name="quantity"/>
                                <field name="purchase_price"/>
                                <field name="total" sum="Total"/>
                            </tree>
                        </field>
                        <group class="oe_subtotal_footer oe_right">
                             <field name="preinvoice_total" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        </group>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>