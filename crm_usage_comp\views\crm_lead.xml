<odoo>
<record id="type_usage_button" model="ir.ui.view">
    <field name="name">ccrm.lead.type_usage.button</field>
    <field name="model">crm.lead</field>
    <!-- Inherit your existing view that already added the 'Contrat' page -->
    <field name="inherit_id" ref="crm_type_usage.type_usage"/> 
    <field name="arch" type="xml">
        <xpath expr="//page[@name='Contrat']" position="inside">
            <div class="o_horizontal" style="display: flex; align-items: center; gap: 10px;" attrs="{'invisible': [('installation_type', '!=', 'connected')]}">&gt;
                <label for="contrat_steg_generated" string="Contrat STEG généré"/>
                <field name="contrat_steg_generated" readonly="1"/>
                <button name="generate_contrat_steg"
                    string="Générer Contrat Steg"
                    type="object"
                    class="oe_highlight"/>
            </div>
        </xpath>
    </field>
</record>
</odoo>
